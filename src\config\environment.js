/**
 * Environment Configuration Loader
 * 
 * Dynamically loads the appropriate environment configuration
 * based on the current Vite mode (development/production)
 * 
 * Usage:
 * import env from './config/environment';
 * 
 * // In an async function or top-level await:
 * const config = await env;
 * console.log(config.api.baseUrl);
 */

let config;

// Determine which environment config to load
switch (import.meta.env.MODE) {
  case 'development':
    config = await import('../../environment/development.js');
    break;
  case 'production':
    config = await import('../../environment/production.js');
    break;
  default:
    console.warn(`Unknown environment: ${import.meta.env.MODE}. Falling back to development.`);
    config = await import('../../environment/development.js');
}

export default config.default;
