/**
 * API Services - Centralized API service exports
 */
import ApiClient from './client.js';
import AuthAPI from './auth.js';
import UserAPI from './user.js';
import ContentAPI from './content.js';
import env from '../../config/environment.js';

// Initialize API client and services
const initializeAPI = async () => {
  const config = await env;
  const apiClient = new ApiClient(config.api.baseUrl);
  
  return {
    client: apiClient,
    auth: new AuthAPI(apiClient),
    user: new UserAPI(apiClient),
    content: new ContentAPI(apiClient),
  };
};

// Export classes for testing
export { ApiClient, AuthAPI, UserAPI, ContentAPI };

// Export default initialized API services
export default initializeAPI();
