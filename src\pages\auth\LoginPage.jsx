import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import { ReCaptcha } from '../../components/ui';
import '../../styles/LoginPage.css';
import { auth, googleProvider, microsoftProvider, facebookProvider } from '../../firebase';
import { signInWithPopup } from 'firebase/auth';


const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const [showPassword, setShowPassword] = useState(false);
  const [captchaToken, setCaptchaToken] = useState(null);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [captchaError, setCaptchaError] = useState('');

  const recaptchaRef = useRef(null);


  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);

  const handleSubmit = (e) => {
    e.preventDefault();


    let valid = true;
    setEmailError('');
    setPasswordError('');
    setCaptchaError('');


    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required.');
      valid = false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address.');
      valid = false;
    }

    if (!password) {
      setPasswordError('Password is required.');
      valid = false;
    }

    if (!captchaToken) {
      setCaptchaError('Please complete the CAPTCHA.');
      valid = false;
    }

    if (!valid) return;

    const success = login(email, password, captchaToken);
    if (success) {
      navigate('/readingScreen');
    } else {
      setPasswordError('Invalid credentials. Please try again.');
    }
  };

  const handleGoogleSignIn = () => {
    signInWithPopup(auth, googleProvider).then((result) => {
      useAuthStore.getState().thirdPartyLogin(result.user);
      navigate('/readingScreen');
      console.log("User signed in with Google 1", result.user);
      console.log("User signed in with Google 2", result.operationType);
      console.log("User signed in with Google 3", result.providerId);
      console.log("User signed in with Google 4", result.user.getIdToken());
      console.log("User signed in with Google 5", result.user.getIdTokenResult());
      console.log("User signed in with Google 6", result.user.providerId);
    }).catch((error) => {
      console.log("Error signing in with Google", error);
    });
  };

  const handleMicrosoftSignIn = () => {
    signInWithPopup(auth, microsoftProvider).then((result) => {
      useAuthStore.getState().thirdPartyLogin(result.user);
      navigate('/readingScreen');
      console.log("User signed in with Microsoft 1", result.user);
      console.log("User signed in with Microsoft 2", result.operationType);
      console.log("User signed in with Microsoft 3", result.providerId);
      console.log("User signed in with Microsoft 4", result.user.getIdToken());
      console.log("User signed in with Microsoft 5", result.user.getIdTokenResult());
      console.log("User signed in with Microsoft 6", result.user.providerId);
    }).catch((error) => {
      console.log("Error signing in with Microsoft", error);
    });
  }

  const handleFacebookSignIn = () => {
    signInWithPopup(auth, facebookProvider).then((result) => {
      useAuthStore.getState().thirdPartyLogin(result.user);
      navigate('/readingScreen');
      console.log("User signed in with Facebook 1", result.user);
      console.log("User signed in with Facebook 2", result.operationType);
      console.log("User signed in with Facebook 3", result.providerId);
      console.log("User signed in with Facebook 4", result.user.getIdToken());
      console.log("User signed in with Facebook 5", result.user.getIdTokenResult());
      console.log("User signed in with Facebook 6", result.user.providerId);
    }).catch((error) => {
      console.log("Error signing in with Facebook", error);
    });
  }


  return (
    <div className="login-container">

      <Header />
      <AnimationBox className="login-box">

        <h4 className='h4'>Sign In</h4>

        <div className='social-login'>
          <button className='social-btn google body2 ' onClick={handleGoogleSignIn}>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='google' /></button>
          <button className='social-btn microsoft body2' onClick={handleMicrosoftSignIn}>Continue with Microsoft<img src='/icons/microsoft.svg' alt='google' /></button>
          <button className='social-btn facebook body2' onClick={handleFacebookSignIn}>Continue with Facebook<img src='/icons/facebook.svg' alt='google' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."

            />

            {emailError && <div className="error-message">{emailError}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <div className="password-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."

              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
            </div>
            {passwordError && <div className="error-message">{passwordError}</div>}
          </div>

          <ReCaptcha
            ref={recaptchaRef}
            onChange={(token) => setCaptchaToken(token)}
            showError={false}
          />
          {captchaError && <div className="error-message">{captchaError}</div>}

          <button type="submit" className="body3-bold login-button">Sign in</button>
        </form>
        <div className='body4 forgot-password'><a href="/forgot-password">Forgot password?</a></div>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>New to the platform?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/signup')}
          >
            Sign up
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default LoginPage;