import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import axios from 'axios';
import ApiService from '../api.js';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

describe('ApiService', () => {
  let apiService;
  let mockAxiosInstance;

  beforeEach(() => {
    // Create mock axios instance
    mockAxiosInstance = {
      post: vi.fn(),
      get: vi.fn(),
      put: vi.fn(),
      patch: vi.fn(),
      delete: vi.fn(),
      defaults: {
        headers: {
          common: {}
        }
      },
      interceptors: {
        request: {
          use: vi.fn()
        },
        response: {
          use: vi.fn()
        }
      }
    };

    // Mock axios.create to return our mock instance
    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    apiService = new ApiService('https://api.example.com');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should create axios instance with correct config', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://api.example.com',
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });

    it('should set up request and response interceptors', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });
  });

  describe('POST request', () => {
    it('should make POST request successfully', async () => {
      const mockResponse = { data: { message: 'Success' } };
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await apiService.post('/test', { key: 'value' });

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/test', { key: 'value' }, {});
      expect(result).toEqual({ message: 'Success' });
    });

    it('should handle POST request error', async () => {
      const mockError = new Error('Request failed');
      mockAxiosInstance.post.mockRejectedValue(mockError);

      await expect(apiService.post('/test', { key: 'value' })).rejects.toThrow('Request failed');
    });
  });

  describe('GET request', () => {
    it('should make GET request successfully', async () => {
      const mockResponse = { data: { message: 'Success' } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const result = await apiService.get('/test');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/test', {});
      expect(result).toEqual({ message: 'Success' });
    });
  });

  describe('PUT request', () => {
    it('should make PUT request successfully', async () => {
      const mockResponse = { data: { message: 'Updated' } };
      mockAxiosInstance.put.mockResolvedValue(mockResponse);

      const result = await apiService.put('/test', { key: 'value' });

      expect(mockAxiosInstance.put).toHaveBeenCalledWith('/test', { key: 'value' }, {});
      expect(result).toEqual({ message: 'Updated' });
    });
  });

  describe('PATCH request', () => {
    it('should make PATCH request successfully', async () => {
      const mockResponse = { data: { message: 'Patched' } };
      mockAxiosInstance.patch.mockResolvedValue(mockResponse);

      const result = await apiService.patch('/test', { key: 'value' });

      expect(mockAxiosInstance.patch).toHaveBeenCalledWith('/test', { key: 'value' }, {});
      expect(result).toEqual({ message: 'Patched' });
    });
  });

  describe('DELETE request', () => {
    it('should make DELETE request successfully', async () => {
      const mockResponse = { data: { message: 'Deleted' } };
      mockAxiosInstance.delete.mockResolvedValue(mockResponse);

      const result = await apiService.delete('/test');

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/test', {});
      expect(result).toEqual({ message: 'Deleted' });
    });
  });

  describe('Authentication', () => {
    it('should set auth token', () => {
      apiService.setAuthToken('test-token');

      expect(mockAxiosInstance.defaults.headers.common['Authorization']).toBe('Bearer test-token');
    });

    it('should remove auth token', () => {
      apiService.setAuthToken('test-token');
      apiService.setAuthToken(null);

      expect(mockAxiosInstance.defaults.headers.common['Authorization']).toBeUndefined();
    });
  });

  describe('Custom headers', () => {
    it('should set custom header', () => {
      apiService.setHeader('X-Custom-Header', 'custom-value');

      expect(mockAxiosInstance.defaults.headers.common['X-Custom-Header']).toBe('custom-value');
    });

    it('should remove custom header', () => {
      apiService.setHeader('X-Custom-Header', 'custom-value');
      apiService.removeHeader('X-Custom-Header');

      expect(mockAxiosInstance.defaults.headers.common['X-Custom-Header']).toBeUndefined();
    });
  });
});
