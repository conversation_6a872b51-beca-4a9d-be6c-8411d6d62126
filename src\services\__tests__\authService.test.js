import { describe, it, expect, beforeEach, vi } from 'vitest';
import AuthService from '../authService.js';

// Mock the API client
vi.mock('../../api/index.js', () => ({
  apiClient: {
    post: vi.fn(),
    setAuthToken: vi.fn(),
  },
  API_ENDPOINTS: {
    auth: {
      emailVerification: '/auth/email-verification',
      verifyOTP: '/auth/verify-otp',
      createAccount: '/auth/create-account',
      login: '/auth/login',
      logout: '/auth/logout',
      resendVerification: '/auth/resend-verification',
    }
  },
  withErrorHandling: vi.fn(),
}));

describe('AuthService', () => {
  let authService;
  let mockApiClient;
  let mockWithErrorHandling;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Import the mocked modules
    const { apiClient, withErrorHandling } = await import('../../api/index.js');
    mockApiClient = apiClient;
    mockWithErrorHandling = withErrorHandling;

    authService = new AuthService();
  });

  describe('sendEmailVerification', () => {
    it('should send email verification successfully', async () => {
      const mockResponse = { success: true, data: { message: 'Verification email sent' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'sendEmailVerification', email: '<EMAIL>' }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle email verification failure', async () => {
      const mockError = { success: false, error: 'API Error' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(result).toEqual(mockError);
    });
  });


});
