import { describe, it, expect, beforeEach, vi } from 'vitest';
import AuthService from '../authService.js';

// Mock API service
const mockApiService = {
  post: vi.fn(),
};

describe('AuthService', () => {
  let authService;

  beforeEach(() => {
    vi.clearAllMocks();
    authService = new AuthService(mockApiService);
  });

  describe('sendEmailVerification', () => {
    it('should send email verification successfully', async () => {
      const mockResponse = { message: 'Verification email sent' };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/email-verification', {
        email: '<EMAIL>',
        purpose: 'sign_up',
        recaptchaToken: 'captcha-token'
      });

      expect(result).toEqual({
        success: true,
        data: mockResponse
      });
    });

    it('should handle email verification failure', async () => {
      const mockError = new Error('API Error');
      mockApiService.post.mockRejectedValue(mockError);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(result).toEqual({
        success: false,
        error: 'API Error'
      });
    });

    it('should handle email verification failure with default message', async () => {
      const mockError = new Error();
      mockApiService.post.mockRejectedValue(mockError);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(result).toEqual({
        success: false,
        error: 'Failed to send email verification'
      });
    });
  });


});
