import { describe, it, expect, beforeEach, vi } from 'vitest';
import AuthService from '../authService.js';

// Mock API service
const mockApiService = {
  post: vi.fn(),
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
};

describe('AuthService', () => {
  let authService;

  beforeEach(() => {
    vi.clearAllMocks();
    authService = new AuthService(mockApiService);
  });

  describe('sendEmailVerification', () => {
    it('should send email verification successfully', async () => {
      const mockResponse = { message: 'Verification email sent' };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/email-verification', {
        email: '<EMAIL>',
        purpose: 'sign_up',
        recaptchaToken: 'captcha-token'
      });

      expect(result).toEqual({
        success: true,
        data: mockResponse
      });
    });

    it('should handle email verification failure', async () => {
      const mockError = new Error('API Error');
      mockApiService.post.mockRejectedValue(mockError);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(result).toEqual({
        success: false,
        error: 'API Error'
      });
    });

    it('should handle email verification failure with default message', async () => {
      const mockError = new Error();
      mockApiService.post.mockRejectedValue(mockError);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(result).toEqual({
        success: false,
        error: 'Failed to send email verification'
      });
    });
  });

  describe('verifyOTP', () => {
    it('should verify OTP successfully', async () => {
      const mockResponse = { message: 'OTP verified' };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.verifyOTP('<EMAIL>', '123456');

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/verify-otp', {
        email: '<EMAIL>',
        otp: '123456'
      });

      expect(result).toEqual({
        success: true,
        data: mockResponse
      });
    });

    it('should handle OTP verification failure', async () => {
      const mockError = new Error('Invalid OTP');
      mockApiService.post.mockRejectedValue(mockError);

      const result = await authService.verifyOTP('<EMAIL>', '123456');

      expect(result).toEqual({
        success: false,
        error: 'Invalid OTP'
      });
    });
  });

  describe('createAccount', () => {
    it('should create account successfully', async () => {
      const mockResponse = { message: 'Account created' };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.createAccount('<EMAIL>', 'password123', 'password123');

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/create-account', {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      });

      expect(result).toEqual({
        success: true,
        data: mockResponse
      });
    });
  });

  describe('login', () => {
    it('should login successfully', async () => {
      const mockResponse = { token: 'jwt-token', user: { id: 1, email: '<EMAIL>' } };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.login('<EMAIL>', 'password123', 'captcha-token');

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/login', {
        email: '<EMAIL>',
        password: 'password123',
        recaptchaToken: 'captcha-token'
      });

      expect(result).toEqual({
        success: true,
        data: mockResponse
      });
    });
  });

  describe('resendEmailVerification', () => {
    it('should resend email verification successfully', async () => {
      const mockResponse = { message: 'Verification email resent' };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.resendEmailVerification('<EMAIL>');

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/resend-verification', {
        email: '<EMAIL>',
        purpose: 'sign_up'
      });

      expect(result).toEqual({
        success: true,
        data: mockResponse
      });
    });
  });
});
