/**
 * Services - Main service layer exports
 */
import AuthService from './authService.js';
import initializeAPI from './api/index.js';

// Initialize all services
const initializeServices = async () => {
  const api = await initializeAPI;

  return {
    // API layer (direct access if needed)
    api,

    // Service layer (business logic)
    authService: new AuthService(api.auth),

    // You can add more service layers here as needed
    // userService: new UserService(api.user),
    // contentService: new ContentService(api.content),
  };
};

// Export service classes for testing
export { AuthService, initializeServices };
