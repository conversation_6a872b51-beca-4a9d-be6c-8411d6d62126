/**
 * Services - API services and external integrations
 */
import ApiService from './api.js';
import AuthService from './authService.js';
import env from '../config/environment.js';

// Initialize API service with base URL from environment
const initializeServices = async () => {
  const config = await env;
  const apiService = new ApiService(config.api.baseUrl);
  const authService = new AuthService(apiService);

  return {
    apiService,
    authService
  };
};

// Export services
export { ApiService, AuthService, initializeServices };
