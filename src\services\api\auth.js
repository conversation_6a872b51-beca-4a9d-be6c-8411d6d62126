/**
 * Auth API Service - Authentication-related API endpoints
 */

class AuthAPI {
  constructor(apiClient) {
    this.client = apiClient;
  }

  /**
   * Send email verification for signup
   * @param {string} email - User email address
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} API response
   */
  async sendEmailVerification(email, recaptchaToken) {
    return await this.client.post('/auth/email-verification', {
      email,
      purpose: 'sign_up',
      recaptchaToken
    });
  }

  /**
   * Verify OTP code
   * @param {string} email - User email address
   * @param {string} otp - OTP code
   * @returns {Promise} API response
   */
  async verifyOTP(email, otp) {
    return await this.client.post('/auth/verify-otp', {
      email,
      otp
    });
  }

  /**
   * Resend email verification
   * @param {string} email - User email address
   * @returns {Promise} API response
   */
  async resendEmailVerification(email) {
    return await this.client.post('/auth/resend-verification', {
      email,
      purpose: 'sign_up'
    });
  }

  /**
   * Create user account with password
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} API response
   */
  async createAccount(email, password, confirmPassword) {
    return await this.client.post('/auth/create-account', {
      email,
      password,
      confirmPassword
    });
  }

  /**
   * Login user
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} API response
   */
  async login(email, password, recaptchaToken) {
    return await this.client.post('/auth/login', {
      email,
      password,
      recaptchaToken
    });
  }

  /**
   * Logout user
   * @returns {Promise} API response
   */
  async logout() {
    return await this.client.post('/auth/logout');
  }

  /**
   * Refresh authentication token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise} API response
   */
  async refreshToken(refreshToken) {
    return await this.client.post('/auth/refresh', {
      refreshToken
    });
  }

  /**
   * Request password reset
   * @param {string} email - User email address
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} API response
   */
  async requestPasswordReset(email, recaptchaToken) {
    return await this.client.post('/auth/forgot-password', {
      email,
      recaptchaToken
    });
  }

  /**
   * Reset password with token
   * @param {string} token - Reset token
   * @param {string} newPassword - New password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} API response
   */
  async resetPassword(token, newPassword, confirmPassword) {
    return await this.client.post('/auth/reset-password', {
      token,
      newPassword,
      confirmPassword
    });
  }

  /**
   * Change password (authenticated user)
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} API response
   */
  async changePassword(currentPassword, newPassword, confirmPassword) {
    return await this.client.put('/auth/change-password', {
      currentPassword,
      newPassword,
      confirmPassword
    });
  }
}

export default AuthAPI;
