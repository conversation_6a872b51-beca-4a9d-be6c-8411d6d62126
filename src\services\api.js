/**
 * API Service - HTTP client for making API requests using Axios
 */
import axios from 'axios';

class ApiService {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;

    // Create axios instance with default configuration
    this.axiosInstance = axios.create({
      baseURL: baseUrl,
      timeout: 10000, // 10 seconds timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor for logging
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(`Making ${config.method?.toUpperCase()} request to: ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        console.error('API request failed:', error);

        // Handle different types of errors
        if (error.response) {
          // Server responded with error status
          const errorMessage = error.response.data?.message ||
                              error.response.data?.error ||
                              error.response.statusText ||
                              `HTTP error! status: ${error.response.status}`;
          throw new Error(errorMessage);
        } else if (error.request) {
          // Request was made but no response received
          throw new Error('Network error: No response received from server');
        } else {
          // Something else happened
          throw new Error(error.message || 'An unexpected error occurred');
        }
      }
    );
  }

  /**
   * POST request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} config - Additional axios config
   * @returns {Promise} Response promise
   */
  async post(endpoint, data = null, config = {}) {
    try {
      const response = await this.axiosInstance.post(endpoint, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * GET request
   * @param {string} endpoint - API endpoint
   * @param {Object} config - Additional axios config
   * @returns {Promise} Response promise
   */
  async get(endpoint, config = {}) {
    try {
      const response = await this.axiosInstance.get(endpoint, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PUT request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} config - Additional axios config
   * @returns {Promise} Response promise
   */
  async put(endpoint, data = null, config = {}) {
    try {
      const response = await this.axiosInstance.put(endpoint, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PATCH request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} config - Additional axios config
   * @returns {Promise} Response promise
   */
  async patch(endpoint, data = null, config = {}) {
    try {
      const response = await this.axiosInstance.patch(endpoint, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * DELETE request
   * @param {string} endpoint - API endpoint
   * @param {Object} config - Additional axios config
   * @returns {Promise} Response promise
   */
  async delete(endpoint, config = {}) {
    try {
      const response = await this.axiosInstance.delete(endpoint, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Set authorization header
   * @param {string} token - Authorization token
   */
  setAuthToken(token) {
    if (token) {
      this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.axiosInstance.defaults.headers.common['Authorization'];
    }
  }

  /**
   * Set custom header
   * @param {string} key - Header key
   * @param {string} value - Header value
   */
  setHeader(key, value) {
    this.axiosInstance.defaults.headers.common[key] = value;
  }

  /**
   * Remove custom header
   * @param {string} key - Header key
   */
  removeHeader(key) {
    delete this.axiosInstance.defaults.headers.common[key];
  }
}

export default ApiService;
