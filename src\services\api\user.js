/**
 * User API Service - User-related API endpoints
 */

class UserAPI {
  constructor(apiClient) {
    this.client = apiClient;
  }

  /**
   * Get user profile
   * @returns {Promise} API response
   */
  async getProfile() {
    return await this.client.get('/user/profile');
  }

  /**
   * Update user profile
   * @param {Object} profileData - Profile data to update
   * @returns {Promise} API response
   */
  async updateProfile(profileData) {
    return await this.client.put('/user/profile', profileData);
  }

  /**
   * Upload user avatar
   * @param {File} file - Avatar file
   * @returns {Promise} API response
   */
  async uploadAvatar(file) {
    const formData = new FormData();
    formData.append('avatar', file);
    
    return await this.client.post('/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Delete user avatar
   * @returns {Promise} API response
   */
  async deleteAvatar() {
    return await this.client.delete('/user/avatar');
  }

  /**
   * Get user preferences
   * @returns {Promise} API response
   */
  async getPreferences() {
    return await this.client.get('/user/preferences');
  }

  /**
   * Update user preferences
   * @param {Object} preferences - User preferences
   * @returns {Promise} API response
   */
  async updatePreferences(preferences) {
    return await this.client.put('/user/preferences', preferences);
  }

  /**
   * Get user activity history
   * @param {Object} params - Query parameters (page, limit, etc.)
   * @returns {Promise} API response
   */
  async getActivityHistory(params = {}) {
    return await this.client.get('/user/activity', { params });
  }

  /**
   * Delete user account
   * @param {string} password - User password for confirmation
   * @returns {Promise} API response
   */
  async deleteAccount(password) {
    return await this.client.delete('/user/account', {
      data: { password }
    });
  }
}

export default UserAPI;
