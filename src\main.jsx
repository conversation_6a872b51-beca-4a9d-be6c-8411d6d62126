import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import env from './config/environment';
import App from './App.jsx';

// Log environment information
console.log(`Running in ${import.meta.env.MODE} mode`);

// This will be called after the environment is loaded
const initApp = async () => {
  const config = await env;
  
  console.log('Environment Configuration:', {
    mode: import.meta.env.MODE,
    baseUrl: config.api.baseUrl,
    environment: config.env,
    isProduction: config.isProduction,
    isDevelopment: config.isDevelopment
  });

  createRoot(document.getElementById('root')).render(
    <StrictMode>
      <App config={config} />
    </StrictMode>
  );
};

initApp().catch(console.error);
