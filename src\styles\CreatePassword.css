.password-description {
  text-align: center;
  margin: 1.5rem 0;
}

.password-description p {
  margin: 0.25rem 0;
  color: var(--secondary-text-color);
}

.password-requirements {
  margin: 1rem 0;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid var(--border-color);
}

.password-requirements p {
  margin: 0 0 0.5rem 0;
  color: var(--secondary-text-color);
  font-weight: 500;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  display: flex;
  align-items: center;
  margin: 0.25rem 0;
  font-size: var(--body4-size);
  line-height: var(--body4-line-height);
  position: relative;
  padding-left: 1.5rem;
}

.requirements-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid;
  display: flex;
  align-items: center;
  justify-content: center;
}

.requirements-list li.valid {
  color: #22c55e;
}

.requirements-list li.valid::before {
  border-color: #22c55e;
  background-color: #22c55e;
}

.requirements-list li.valid::after {
  content: '✓';
  position: absolute;
  left: 0.125rem;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
}

.requirements-list li.invalid {
  color: var(--secondary-text-color);
}

.requirements-list li.invalid::before {
  border-color: var(--border-color);
  background-color: transparent;
}

.login-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.login-button:disabled:hover {
  background-color: var(--text-color);
}

@media (max-width: 768px) {
  .password-requirements {
    padding: 0.75rem;
  }
  
  .requirements-list li {
    font-size: 0.8rem;
  }
}