import { useEffect, useState } from 'react';
import OtpInput from 'react-otp-input';
import { useLocation, useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import '../../styles/LoginPage.css';
import '../../styles/OTPVerification.css';

const OTPVerificationPage = () => {
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || '';

  useEffect(() => {
    if (!email) {
      navigate('/signup');
      return;
    }

    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [email, navigate]);

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP.');
      return;
    }

    // Simulate OTP verification (replace with actual API call)
    if (otp === '123456') {
      // Navigate to create password screen
      navigate('/create-password', { state: { email } });
    } else {
      setError('Invalid OTP. Please try again.');
    }
  };

  const handleResendOTP = () => {
    if (canResend) {
      // Simulate resending OTP (replace with actual API call)
      setTimer(60);
      setCanResend(false);
      setError('');
      setOtp('');

      const interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="login-box">
        <h4 className='h4'>Verify Your Email</h4>

        <div className="otp-description">
          <p className="body2">We've sent a 6-digit verification code to</p>
          <p className="body3-bold email-display">{email}</p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="otp">Enter verification code:</label>
            <div className="otp-container">
              <OtpInput
                value={otp}
                onChange={setOtp}
                numInputs={6}
                renderSeparator={<span className="otp-separator"></span>}
                renderInput={(inputProps, idx) => (
                  <input {...inputProps} className="otp-input" key={idx} />
                )}
                inputType="number"
                shouldAutoFocus={true}
              />
            </div>
            {error && <div className="error-message">{error}</div>}
          </div>

          <button
            type="submit"
            className="body3-bold login-button"
            disabled={otp.length !== 6}
          >
            Verify Code
          </button>
        </form>

        <div className="resend-section">
          {!canResend ? (
            <p className="body4">Resend code in {formatTime(timer)}</p>
          ) : (
            <button
              className="body3-bold resend-button"
              onClick={handleResendOTP}
            >
              Resend Code
            </button>
          )}
        </div>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>Wrong email?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/signup')}
          >
            Go back
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default OTPVerificationPage;