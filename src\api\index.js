/**
 * API Module - Export configured API client and utilities
 */
import apiClient from './client.js';
import API_ENDPOINTS, { buildEndpoint, withQueryParams, validateEndpoint } from './endpoints.js';
import errorHandler, { withErrorHandling, ERROR_TYPES } from './errorHandler.js';

// Export the configured API client
export { default as apiClient } from './client.js';

// Export endpoint utilities
export { 
  API_ENDPOINTS, 
  buildEndpoint, 
  withQueryParams, 
  validateEndpoint 
} from './endpoints.js';

// Export error handling utilities
export { 
  default as errorHandler,
  withErrorHandling,
  ERROR_TYPES,
  ERROR_MESSAGES,
  handleApiError,
  isRetryableError
} from './errorHandler.js';

// Default export with all utilities
export default {
  client: apiClient,
  endpoints: API_ENDPOINTS,
  errorHandler,
  utils: {
    buildEndpoint,
    withQueryParams,
    validateEndpoint,
    withErrorHandling,
  },
};
