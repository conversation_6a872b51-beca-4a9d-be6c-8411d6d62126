/**
 * Authentication Service - Handles authentication-related API calls
 */
import ApiService from './api.js';

class AuthService {
  constructor(apiService) {
    this.api = apiService;
  }

  /**
   * Send email verification for signup
   * @param {string} email - User email address
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} API response
   */
  async sendEmailVerification(email, recaptchaToken) {
    try {
      const response = await this.api.post('/auth/email-verification', {
        email,
        purpose: 'sign_up',
        recaptchaToken
      });

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Email verification failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email verification'
      };
    }
  }

  /**
   * Verify OTP code
   * @param {string} email - User email address
   * @param {string} otp - OTP code
   * @returns {Promise} API response
   */
  async verifyOTP(email, otp) {
    try {
      const response = await this.api.post('/auth/verify-otp', {
        email,
        otp
      });

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('OTP verification failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to verify OTP'
      };
    }
  }

  /**
   * Create user account with password
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} API response
   */
  async createAccount(email, password, confirmPassword) {
    try {
      const response = await this.api.post('/auth/create-account', {
        email,
        password,
        confirmPassword
      });

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Account creation failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to create account'
      };
    }
  }

  /**
   * Login user
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} API response
   */
  async login(email, password, recaptchaToken) {
    try {
      const response = await this.api.post('/auth/login', {
        email,
        password,
        recaptchaToken
      });

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Login failed:', error);
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }

  /**
   * Resend email verification
   * @param {string} email - User email address
   * @returns {Promise} API response
   */
  async resendEmailVerification(email) {
    try {
      const response = await this.api.post('/auth/resend-verification', {
        email,
        purpose: 'sign_up'
      });

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Resend verification failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to resend verification email'
      };
    }
  }
}

export default AuthService;
