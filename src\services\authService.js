/**
 * Authentication Service - Handles authentication-related API calls
 */
import ApiService from './api.js';

class AuthService {
  constructor(apiService) {
    this.api = apiService;
  }

  /**
   * Send email verification for signup
   * @param {string} email - User email address
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} API response
   */
  async sendEmailVerification(email, recaptchaToken) {
    try {
      const response = await this.api.post('/auth/email-verification', {
        email,
        purpose: 'sign_up',
        recaptchaToken
      });

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Email verification failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email verification'
      };
    }
  }
}

export default AuthService;
