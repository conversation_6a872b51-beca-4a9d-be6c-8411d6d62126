/**
 * Authentication Service - Business logic layer for authentication
 */

class AuthService {
  constructor(authAPI) {
    this.authAPI = authAPI;
  }

  /**
   * Send email verification for signup
   * @param {string} email - User email address
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} Service response
   */
  async sendEmailVerification(email, recaptchaToken) {
    try {
      const response = await this.authAPI.sendEmailVerification(email, recaptchaToken);

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Email verification failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email verification'
      };
    }
  }

  /**
   * Verify OTP code
   * @param {string} email - User email address
   * @param {string} otp - OTP code
   * @returns {Promise} Service response
   */
  async verifyOTP(email, otp) {
    try {
      const response = await this.authAPI.verifyOTP(email, otp);

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('OTP verification failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to verify OTP'
      };
    }
  }

  /**
   * Create user account
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} Service response
   */
  async createAccount(email, password, confirmPassword) {
    try {
      // Validate passwords match
      if (password !== confirmPassword) {
        return {
          success: false,
          error: 'Passwords do not match'
        };
      }

      const response = await this.authAPI.createAccount(email, password, confirmPassword);

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Account creation failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to create account'
      };
    }
  }

  /**
   * Login user
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} Service response
   */
  async login(email, password, recaptchaToken) {
    try {
      const response = await this.authAPI.login(email, password, recaptchaToken);

      // Store auth token if provided
      if (response.token) {
        this.authAPI.client.setAuthToken(response.token);
      }

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Login failed:', error);
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }

  /**
   * Logout user
   * @returns {Promise} Service response
   */
  async logout() {
    try {
      await this.authAPI.logout();

      // Clear auth token
      this.authAPI.client.setAuthToken(null);

      return {
        success: true,
        data: { message: 'Logged out successfully' }
      };
    } catch (error) {
      console.error('Logout failed:', error);
      // Clear token even if API call fails
      this.authAPI.client.setAuthToken(null);

      return {
        success: false,
        error: error.message || 'Logout failed'
      };
    }
  }

  /**
   * Resend email verification
   * @param {string} email - User email address
   * @returns {Promise} Service response
   */
  async resendEmailVerification(email) {
    try {
      const response = await this.authAPI.resendEmailVerification(email);

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('Resend verification failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to resend verification email'
      };
    }
  }
}

export default AuthService;
