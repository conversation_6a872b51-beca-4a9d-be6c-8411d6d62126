import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import '../../styles/LoginPage.css';

const SignupPage = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');

  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    // Navigate to OTP verification screen
    navigate('/otp-verification', { state: { email } });
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="login-box">
        <h4 className='h4'>Sign Up</h4>

        <div className='social-login'>
          <button className='social-btn google body2'>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='apple' /></button>
          <button className='social-btn microsoft body2'>Continue with Microsoft<img src='/icons/microsoft.svg' alt='microsoft' /></button>
          <button className='social-btn facebook body2'>Continue with Facebook<img src='/icons/facebook.svg' alt='facebook' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error && <div className="error-message">{error}</div>}
          </div>

          <button type="submit" className="body3-bold login-button">Continue</button>
        </form>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>Already have an account?</span>
          <button 
            className='body3-bold'
            onClick={() => navigate('/login')}
          >
            Sign in
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default SignupPage;