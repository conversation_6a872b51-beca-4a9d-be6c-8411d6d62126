import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import { ReCaptcha } from '../../components/ui';
import { initializeServices } from '../../services';
import '../../styles/LoginPage.css';

const SignupPage = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [captchaToken, setCaptchaToken] = useState(null);
  const [captchaError, setCaptchaError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  const navigate = useNavigate();
  const recaptchaRef = useRef(null);

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setCaptchaError('');

    let valid = true;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      valid = false;
    }

    if (!captchaToken) {
      setCaptchaError('Please complete the CAPTCHA.');
      valid = false;
    }

    if (!authService) {
      setError('Service not available. Please refresh the page.');
      valid = false;
    }

    if (!valid) return;

    setIsLoading(true);

    try {
      // Call the email verification API
      const result = await authService.sendEmailVerification(email, captchaToken);

      if (result.success) {
        // Navigate to OTP verification screen on success
        navigate('/otp-verification', { state: { email } });
      } else {
        // Show error message
        setError(result.error || 'Failed to send verification email. Please try again.');

        // Reset reCAPTCHA on error
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
          setCaptchaToken(null);
        }
      }
    } catch (error) {
      console.error('Email verification error:', error);
      setError('An unexpected error occurred. Please try again.');

      // Reset reCAPTCHA on error
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
        setCaptchaToken(null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="login-box">
        <h4 className='h4'>Sign Up</h4>

        <div className='social-login'>
          <button className='social-btn google body2'>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='apple' /></button>
          <button className='social-btn microsoft body2'>Continue with Microsoft<img src='/icons/microsoft.svg' alt='microsoft' /></button>
          <button className='social-btn facebook body2'>Continue with Facebook<img src='/icons/facebook.svg' alt='facebook' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error && <div className="error-message">{error}</div>}
          </div>

          <ReCaptcha
            ref={recaptchaRef}
            onChange={(token) => setCaptchaToken(token)}
            showError={false}
          />
          {captchaError && <div className="error-message">{captchaError}</div>}

          <button
            type="submit"
            className="body3-bold login-button"
            disabled={isLoading || !authService}
          >
            {isLoading ? 'Sending...' : 'Continue'}
          </button>
        </form>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>Already have an account?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/login')}
          >
            Sign in
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default SignupPage;