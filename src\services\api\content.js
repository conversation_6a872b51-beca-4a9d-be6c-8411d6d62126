/**
 * Content API Service - Content-related API endpoints
 */

class ContentAPI {
  constructor(apiClient) {
    this.client = apiClient;
  }

  /**
   * Get courses list
   * @param {Object} params - Query parameters (page, limit, search, etc.)
   * @returns {Promise} API response
   */
  async getCourses(params = {}) {
    return await this.client.get('/courses', { params });
  }

  /**
   * Get course by ID
   * @param {string} courseId - Course ID
   * @returns {Promise} API response
   */
  async getCourse(courseId) {
    return await this.client.get(`/courses/${courseId}`);
  }

  /**
   * Get course content/chapters
   * @param {string} courseId - Course ID
   * @returns {Promise} API response
   */
  async getCourseContent(courseId) {
    return await this.client.get(`/courses/${courseId}/content`);
  }

  /**
   * Get PDF content
   * @param {string} contentId - Content ID
   * @returns {Promise} API response
   */
  async getPDFContent(contentId) {
    return await this.client.get(`/content/${contentId}/pdf`);
  }

  /**
   * Get audio content
   * @param {string} contentId - Content ID
   * @returns {Promise} API response
   */
  async getAudioContent(contentId) {
    return await this.client.get(`/content/${contentId}/audio`);
  }

  /**
   * Save user progress
   * @param {string} courseId - Course ID
   * @param {string} contentId - Content ID
   * @param {Object} progressData - Progress data
   * @returns {Promise} API response
   */
  async saveProgress(courseId, contentId, progressData) {
    return await this.client.post(`/courses/${courseId}/progress`, {
      contentId,
      ...progressData
    });
  }

  /**
   * Get user progress
   * @param {string} courseId - Course ID
   * @returns {Promise} API response
   */
  async getProgress(courseId) {
    return await this.client.get(`/courses/${courseId}/progress`);
  }

  /**
   * Save bookmark
   * @param {string} contentId - Content ID
   * @param {Object} bookmarkData - Bookmark data
   * @returns {Promise} API response
   */
  async saveBookmark(contentId, bookmarkData) {
    return await this.client.post(`/content/${contentId}/bookmarks`, bookmarkData);
  }

  /**
   * Get bookmarks
   * @param {string} contentId - Content ID
   * @returns {Promise} API response
   */
  async getBookmarks(contentId) {
    return await this.client.get(`/content/${contentId}/bookmarks`);
  }

  /**
   * Delete bookmark
   * @param {string} bookmarkId - Bookmark ID
   * @returns {Promise} API response
   */
  async deleteBookmark(bookmarkId) {
    return await this.client.delete(`/bookmarks/${bookmarkId}`);
  }

  /**
   * Save highlight
   * @param {string} contentId - Content ID
   * @param {Object} highlightData - Highlight data
   * @returns {Promise} API response
   */
  async saveHighlight(contentId, highlightData) {
    return await this.client.post(`/content/${contentId}/highlights`, highlightData);
  }

  /**
   * Get highlights
   * @param {string} contentId - Content ID
   * @returns {Promise} API response
   */
  async getHighlights(contentId) {
    return await this.client.get(`/content/${contentId}/highlights`);
  }

  /**
   * Delete highlight
   * @param {string} highlightId - Highlight ID
   * @returns {Promise} API response
   */
  async deleteHighlight(highlightId) {
    return await this.client.delete(`/highlights/${highlightId}`);
  }

  /**
   * Save annotation
   * @param {string} contentId - Content ID
   * @param {Object} annotationData - Annotation data
   * @returns {Promise} API response
   */
  async saveAnnotation(contentId, annotationData) {
    return await this.client.post(`/content/${contentId}/annotations`, annotationData);
  }

  /**
   * Get annotations
   * @param {string} contentId - Content ID
   * @returns {Promise} API response
   */
  async getAnnotations(contentId) {
    return await this.client.get(`/content/${contentId}/annotations`);
  }

  /**
   * Update annotation
   * @param {string} annotationId - Annotation ID
   * @param {Object} annotationData - Updated annotation data
   * @returns {Promise} API response
   */
  async updateAnnotation(annotationId, annotationData) {
    return await this.client.put(`/annotations/${annotationId}`, annotationData);
  }

  /**
   * Delete annotation
   * @param {string} annotationId - Annotation ID
   * @returns {Promise} API response
   */
  async deleteAnnotation(annotationId) {
    return await this.client.delete(`/annotations/${annotationId}`);
  }
}

export default ContentAPI;
